using UnityEngine;
using UnityEditor;
using Unity.AI.Navigation;

/// <summary>
/// 🚀 Enhanced 5v5 TDM Setup with All Features
/// Complete setup for 5v5 matches with spectator mode, match logging, and tournament system
/// </summary>
public class Enhanced5v5Setup : EditorWindow
{
    [Header("🏟️ Arena Setup")]
    public GameObject tdmArenaPrefab;
    public bool fixArenaPosition = true;
    public Vector3 arenaScale = new Vector3(0.1f, 0.1f, 0.1f);

    [Header("🤖 Agent Setup")]
    public GameObject victorAgentPrefab;
    public int teamSize = 5;

    [Header("📹 Camera Setup")]
    public bool setupSpectatorCamera = true;
    public Vector3 spectatorStartPosition = new Vector3(0, 15, -20);

    [MenuItem("SquadMate AI/🚀 Enhanced 5v5 Setup")]
    public static void ShowWindow()
    {
        Enhanced5v5Setup window = GetWindow<Enhanced5v5Setup>("Enhanced 5v5 Setup");
        window.minSize = new Vector2(400, 600);
    }

    void OnGUI()
    {
        GUILayout.Label("🚀 Enhanced 5v5 TDM Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This will set up a complete 5v5 TDM environment with:\n" +
                               "• Fixed TDM arena positioning\n" +
                               "• 5v5 AI agent teams\n" +
                               "• Spectator camera system\n" +
                               "• Match logging & statistics\n" +
                               "• Tournament & ELO system\n" +
                               "• Agent tooltips", MessageType.Info);

        GUILayout.Space(10);

        // Arena Setup
        GUILayout.Label("🏟️ Arena Configuration", EditorStyles.boldLabel);
        tdmArenaPrefab = EditorGUILayout.ObjectField("TDM Arena Prefab", tdmArenaPrefab, typeof(GameObject), false) as GameObject;
        fixArenaPosition = EditorGUILayout.Toggle("Fix Arena Position", fixArenaPosition);
        arenaScale = EditorGUILayout.Vector3Field("Arena Scale", arenaScale);

        GUILayout.Space(10);

        // Agent Setup
        GUILayout.Label("🤖 Agent Configuration", EditorStyles.boldLabel);
        victorAgentPrefab = EditorGUILayout.ObjectField("Victor Agent Prefab", victorAgentPrefab, typeof(GameObject), false) as GameObject;
        teamSize = EditorGUILayout.IntSlider("Team Size", teamSize, 1, 10);

        GUILayout.Space(10);

        // Camera Setup
        GUILayout.Label("📹 Camera Configuration", EditorStyles.boldLabel);
        setupSpectatorCamera = EditorGUILayout.Toggle("Setup Spectator Camera", setupSpectatorCamera);
        spectatorStartPosition = EditorGUILayout.Vector3Field("Camera Start Position", spectatorStartPosition);

        GUILayout.Space(20);

        // Setup Buttons
        if (GUILayout.Button("🔧 Fix TDM Arena Position Only", GUILayout.Height(30)))
        {
            FixTDMArenaPosition();
        }

        if (GUILayout.Button("🚀 Complete 5v5 Setup", GUILayout.Height(40)))
        {
            SetupComplete5v5Environment();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("📦 Create Unity Package", GUILayout.Height(30)))
        {
            CreateUnityPackage();
        }

        GUILayout.Space(10);

        // Status
        ShowCurrentStatus();
    }

    void FixTDMArenaPosition()
    {
        Debug.Log("🔧 Fixing TDM arena position...");

        // Find existing TDM arena
        GameObject arena = GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena") ?? GameObject.Find("PUBG_TDM_Arena");
        
        if (arena == null && tdmArenaPrefab != null)
        {
            // Create new arena if none exists
            arena = PrefabUtility.InstantiatePrefab(tdmArenaPrefab) as GameObject;
            arena.name = "TDM_Arena";
            Debug.Log("✅ Created new TDM arena from prefab");
        }

        if (arena != null)
        {
            // Fix position and scale
            arena.transform.position = Vector3.zero;
            arena.transform.rotation = Quaternion.identity;
            arena.transform.localScale = arenaScale;

            // Ensure it's marked as static for NavMesh
            MakeStaticRecursive(arena.transform);

            Debug.Log($"✅ TDM arena positioned at {arena.transform.position} with scale {arena.transform.localScale}");
        }
        else
        {
            Debug.LogError("❌ No TDM arena found and no prefab assigned!");
        }
    }

    void SetupComplete5v5Environment()
    {
        Debug.Log("🚀 Setting up complete 5v5 TDM environment...");

        // Step 1: Fix arena position
        FixTDMArenaPosition();

        // Step 2: Setup spawn points
        SetupSpawnPoints();

        // Step 3: Setup game manager with enhanced systems
        SetupGameManager();

        // Step 4: Setup spectator camera
        if (setupSpectatorCamera)
        {
            SetupSpectatorCameraSystem();
        }

        // Step 5: Setup NavMesh
        SetupNavMesh();

        // Step 6: Add tooltips to existing agents
        AddTooltipsToExistingAgents();

        Debug.Log("✅ Complete 5v5 TDM environment setup finished!");
        EditorUtility.DisplayDialog("Setup Complete", 
            "Enhanced 5v5 TDM environment has been set up!\n\n" +
            "Features added:\n" +
            "• Fixed TDM arena positioning\n" +
            "• 5v5 spawn points\n" +
            "• Enhanced SquadManager\n" +
            "• Match logging system\n" +
            "• Tournament & ELO tracking\n" +
            "• Spectator camera\n" +
            "• Agent tooltips\n\n" +
            "Press Play to start the match!", "OK");
    }

    void SetupSpawnPoints()
    {
        Debug.Log("📍 Setting up 5v5 spawn points...");

        GameObject spawnParent = GameObject.Find("SpawnPoints");
        if (spawnParent == null)
        {
            spawnParent = new GameObject("SpawnPoints");
        }

        // Team A spawn points (left side)
        GameObject teamASpawns = new GameObject("TeamA_Spawns");
        teamASpawns.transform.SetParent(spawnParent.transform);

        Vector3[] teamAPositions = {
            new Vector3(-25, 0, 20),
            new Vector3(-30, 0, 15),
            new Vector3(-25, 0, 10),
            new Vector3(-30, 0, 5),
            new Vector3(-25, 0, 0)
        };

        for (int i = 0; i < teamAPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"TeamA_Spawn_{i}");
            spawn.transform.SetParent(teamASpawns.transform);
            spawn.transform.position = teamAPositions[i];
            spawn.tag = "TeamASpawn";
        }

        // Team B spawn points (right side)
        GameObject teamBSpawns = new GameObject("TeamB_Spawns");
        teamBSpawns.transform.SetParent(spawnParent.transform);

        Vector3[] teamBPositions = {
            new Vector3(25, 0, 20),
            new Vector3(30, 0, 15),
            new Vector3(25, 0, 10),
            new Vector3(30, 0, 5),
            new Vector3(25, 0, 0)
        };

        for (int i = 0; i < teamBPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"TeamB_Spawn_{i}");
            spawn.transform.SetParent(teamBSpawns.transform);
            spawn.transform.position = teamBPositions[i];
            spawn.tag = "TeamBSpawn";
        }

        Debug.Log("✅ 5v5 spawn points created");
    }

    void SetupGameManager()
    {
        Debug.Log("🎮 Setting up enhanced game manager...");

        GameObject gameManager = GameObject.Find("GameManager");
        if (gameManager == null)
        {
            gameManager = new GameObject("GameManager");
        }

        // Add SquadManager
        SquadManager squadManager = gameManager.GetComponent<SquadManager>();
        if (squadManager == null)
        {
            squadManager = gameManager.AddComponent<SquadManager>();
        }

        // Configure for 5v5
        squadManager.teamSize = teamSize;
        squadManager.victorAgentPrefab = victorAgentPrefab;

        // Setup spawn point references
        SetupSpawnPointReferences(squadManager);

        Debug.Log("✅ Enhanced game manager configured");
    }

    void SetupSpawnPointReferences(SquadManager squadManager)
    {
        // Find spawn points
        Transform[] teamASpawns = GameObject.Find("TeamA_Spawns")?.GetComponentsInChildren<Transform>();
        Transform[] teamBSpawns = GameObject.Find("TeamB_Spawns")?.GetComponentsInChildren<Transform>();

        if (teamASpawns != null && teamASpawns.Length > 1)
        {
            // Remove parent transform from array
            Transform[] teamASpawnPoints = new Transform[teamASpawns.Length - 1];
            for (int i = 1; i < teamASpawns.Length; i++)
            {
                teamASpawnPoints[i - 1] = teamASpawns[i];
            }
            squadManager.teamASpawnPoints = teamASpawnPoints;
        }

        if (teamBSpawns != null && teamBSpawns.Length > 1)
        {
            // Remove parent transform from array
            Transform[] teamBSpawnPoints = new Transform[teamBSpawns.Length - 1];
            for (int i = 1; i < teamBSpawns.Length; i++)
            {
                teamBSpawnPoints[i - 1] = teamBSpawns[i];
            }
            squadManager.teamBSpawnPoints = teamBSpawnPoints;
        }
    }

    void SetupSpectatorCameraSystem()
    {
        Debug.Log("📹 Setting up spectator camera...");

        Camera mainCam = Camera.main;
        if (mainCam == null)
        {
            // Create new camera
            GameObject camObj = new GameObject("SpectatorCamera");
            mainCam = camObj.AddComponent<Camera>();
            camObj.tag = "MainCamera";
        }

        // Add SpectatorCamera component
        SpectatorCamera spectatorCam = mainCam.GetComponent<SpectatorCamera>();
        if (spectatorCam == null)
        {
            spectatorCam = mainCam.AddComponent<SpectatorCamera>();
        }

        // Position camera
        mainCam.transform.position = spectatorStartPosition;
        mainCam.transform.LookAt(Vector3.zero);

        Debug.Log("✅ Spectator camera system ready");
    }

    void SetupNavMesh()
    {
        Debug.Log("🗺️ Setting up NavMesh...");

        // Find arena
        GameObject arena = GameObject.Find("TDM_Arena") ?? GameObject.Find("Tdm");
        if (arena != null)
        {
            NavMeshSurface navSurface = arena.GetComponent<NavMeshSurface>();
            if (navSurface == null)
            {
                navSurface = arena.AddComponent<NavMeshSurface>();
            }

            navSurface.collectObjects = CollectObjects.Children;
            navSurface.useGeometry = UnityEngine.AI.NavMeshCollectGeometry.RenderMeshes;
            navSurface.BuildNavMesh();

            Debug.Log("✅ NavMesh baked successfully");
        }
    }

    void AddTooltipsToExistingAgents()
    {
        Debug.Log("💡 Adding tooltips to existing agents...");

        VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
        foreach (var agent in agents)
        {
            if (agent.GetComponent<AgentTooltip>() == null)
            {
                agent.gameObject.AddComponent<AgentTooltip>();
            }
        }

        Debug.Log($"✅ Added tooltips to {agents.Length} agents");
    }

    void MakeStaticRecursive(Transform parent)
    {
        parent.gameObject.isStatic = true;
        foreach (Transform child in parent)
        {
            MakeStaticRecursive(child);
        }
    }

    void CreateUnityPackage()
    {
        Debug.Log("📦 Creating Unity package...");

        string[] assetPaths = {
            "Assets/Scripts",
            "Assets/Prefabs",
            "Assets/Materials",
            "Assets/Scenes/TrainingEnvironment.unity"
        };

        string packagePath = EditorUtility.SaveFilePanel(
            "Save Unity Package",
            "",
            "SquadMate-Enhanced-5v5-TDM.unitypackage",
            "unitypackage"
        );

        if (!string.IsNullOrEmpty(packagePath))
        {
            AssetDatabase.ExportPackage(assetPaths, packagePath, ExportPackageOptions.Recurse);
            Debug.Log($"✅ Unity package created: {packagePath}");
            EditorUtility.DisplayDialog("Package Created", $"Unity package saved to:\n{packagePath}", "OK");
        }
    }

    void ShowCurrentStatus()
    {
        GUILayout.Label("📊 Current Status", EditorStyles.boldLabel);

        // Check arena
        GameObject arena = GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena") ?? GameObject.Find("PUBG_TDM_Arena");
        GUILayout.Label($"🏟️ Arena: {(arena != null ? "✅ Found" : "❌ Missing")}");

        // Check spawn points
        GameObject spawns = GameObject.Find("SpawnPoints");
        GUILayout.Label($"📍 Spawn Points: {(spawns != null ? "✅ Found" : "❌ Missing")}");

        // Check game manager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        GUILayout.Label($"🎮 Squad Manager: {(squadManager != null ? "✅ Found" : "❌ Missing")}");

        // Check spectator camera
        SpectatorCamera spectatorCam = FindObjectOfType<SpectatorCamera>();
        GUILayout.Label($"📹 Spectator Camera: {(spectatorCam != null ? "✅ Found" : "❌ Missing")}");

        // Check enhanced systems
        MatchLogger matchLogger = FindObjectOfType<MatchLogger>();
        TournamentManager tournamentManager = FindObjectOfType<TournamentManager>();
        GUILayout.Label($"📊 Match Logger: {(matchLogger != null ? "✅ Found" : "❌ Missing")}");
        GUILayout.Label($"🏆 Tournament Manager: {(tournamentManager != null ? "✅ Found" : "❌ Missing")}");
    }
}
