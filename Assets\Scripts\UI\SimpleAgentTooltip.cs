using UnityEngine;

/// <summary>
/// 💡 Simple Agent Tooltip (No UI Dependencies)
/// Displays agent information using OnGUI when hovering over agents
/// </summary>
public class SimpleAgentTooltip : MonoBehaviour
{
    [Header("🖱️ Tooltip Settings")]
    public float tooltipDelay = 0.5f;
    public bool showDetailedStats = true;
    public Vector2 tooltipSize = new Vector2(250, 200);

    [Header("🎨 Visual Settings")]
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;
    public Color backgroundColor = new Color(0, 0, 0, 0.8f);

    private bool isHovering = false;
    private bool showTooltip = false;
    private float hoverStartTime;
    private Vector3 mousePosition;
    private string tooltipContent = "";

    // Agent components
    private VictorAgent victorAgent;
    private SquadMateAgent squadMateAgent;
    private AgentStats agentStats;
    private HealthSystem healthSystem;
    private InventorySystem inventorySystem;

    void Start()
    {
        // Get agent components
        victorAgent = GetComponent<VictorAgent>();
        squadMateAgent = GetComponent<SquadMateAgent>();
        agentStats = GetComponent<AgentStats>();
        healthSystem = GetComponent<HealthSystem>();
        inventorySystem = GetComponent<InventorySystem>();

        // Add collider if missing (needed for mouse detection)
        if (GetComponent<Collider>() == null)
        {
            CapsuleCollider capsule = gameObject.AddComponent<CapsuleCollider>();
            capsule.height = 2f;
            capsule.radius = 0.5f;
            capsule.isTrigger = true;
        }
    }

    void Update()
    {
        // Check if we should show tooltip after delay
        if (isHovering && !showTooltip && Time.time - hoverStartTime >= tooltipDelay)
        {
            showTooltip = true;
            UpdateTooltipContent();
        }

        // Update mouse position for tooltip placement
        if (showTooltip)
        {
            mousePosition = Input.mousePosition;
        }
    }

    void OnMouseEnter()
    {
        if (!isHovering)
        {
            isHovering = true;
            hoverStartTime = Time.time;
        }
    }

    void OnMouseExit()
    {
        isHovering = false;
        showTooltip = false;
    }

    void OnGUI()
    {
        if (!showTooltip || string.IsNullOrEmpty(tooltipContent)) return;

        // Calculate tooltip position (follow mouse but stay on screen)
        Vector2 tooltipPos = new Vector2(mousePosition.x + 10, Screen.height - mousePosition.y + 10);
        
        // Keep tooltip on screen
        if (tooltipPos.x + tooltipSize.x > Screen.width)
            tooltipPos.x = Screen.width - tooltipSize.x;
        if (tooltipPos.y + tooltipSize.y > Screen.height)
            tooltipPos.y = Screen.height - tooltipSize.y;

        // Create tooltip rect
        Rect tooltipRect = new Rect(tooltipPos.x, tooltipPos.y, tooltipSize.x, tooltipSize.y);

        // Set GUI style
        GUIStyle boxStyle = new GUIStyle(GUI.skin.box);
        boxStyle.normal.background = MakeTex(2, 2, backgroundColor);

        GUIStyle labelStyle = new GUIStyle(GUI.skin.label);
        labelStyle.normal.textColor = Color.white;
        labelStyle.fontSize = 12;
        labelStyle.alignment = TextAnchor.UpperLeft;
        labelStyle.wordWrap = true;

        // Draw tooltip background
        GUI.Box(tooltipRect, "", boxStyle);

        // Draw tooltip content
        Rect contentRect = new Rect(tooltipRect.x + 10, tooltipRect.y + 10, 
                                   tooltipRect.width - 20, tooltipRect.height - 20);
        GUI.Label(contentRect, tooltipContent, labelStyle);
    }

    void UpdateTooltipContent()
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();

        // Agent name and basic info
        sb.AppendLine(gameObject.name);
        sb.AppendLine();

        // Role information
        if (victorAgent != null)
        {
            sb.AppendLine($"Role: {victorAgent.assignedRole}");
            sb.AppendLine($"Team: {victorAgent.teamID}");
        }
        else if (squadMateAgent != null)
        {
            sb.AppendLine("Role: SquadMate");
            sb.AppendLine($"Team: {(gameObject.tag.Contains("TeamA") ? "Team A" : "Team B")}");
        }

        // Health information
        if (healthSystem != null)
        {
            float healthPercent = (healthSystem.currentHealth / healthSystem.maxHealth) * 100f;
            sb.AppendLine($"Health: {healthSystem.currentHealth:F0}/{healthSystem.maxHealth:F0} ({healthPercent:F0}%)");
        }

        // Weapon information
        if (inventorySystem != null)
        {
            string weapon = string.IsNullOrEmpty(inventorySystem.equippedWeapon) ? "None" : inventorySystem.equippedWeapon;
            sb.AppendLine($"Weapon: {weapon}");
            
            if (inventorySystem.medkitCount > 0)
            {
                sb.AppendLine($"Medkits: {inventorySystem.medkitCount}");
            }
        }

        // Detailed stats (if enabled)
        if (showDetailedStats && agentStats != null)
        {
            sb.AppendLine();
            sb.AppendLine("Combat Stats:");
            sb.AppendLine($"Kills: {agentStats.kills}");
            sb.AppendLine($"Deaths: {agentStats.deaths}");
            sb.AppendLine($"K/D: {agentStats.GetKDR():F2}");
            sb.AppendLine($"Revives: {agentStats.revives}");
            sb.AppendLine($"Zones: {agentStats.zonesHeld}");
        }

        // Current status
        sb.AppendLine();
        sb.AppendLine($"Status: {GetAgentStatus()}");

        tooltipContent = sb.ToString();
    }

    string GetAgentStatus()
    {
        if (healthSystem != null && healthSystem.currentHealth <= 0)
        {
            return "Eliminated";
        }

        if (victorAgent != null)
        {
            // Add more specific status based on agent behavior
            if (victorAgent.currentTarget != null)
            {
                return "In Combat";
            }
            else if (inventorySystem != null && inventorySystem.medkitCount > 0 && 
                     healthSystem != null && healthSystem.currentHealth < healthSystem.maxHealth * 0.5f)
            {
                return "Healing";
            }
            else
            {
                return "Patrolling";
            }
        }

        return "Active";
    }

    Color GetTeamColor()
    {
        if (victorAgent != null)
        {
            return victorAgent.teamID == TeamID.TeamA ? teamAColor : teamBColor;
        }
        else if (squadMateAgent != null)
        {
            return gameObject.tag.Contains("TeamA") ? teamAColor : teamBColor;
        }
        return Color.white;
    }

    Texture2D MakeTex(int width, int height, Color col)
    {
        Color[] pix = new Color[width * height];
        for (int i = 0; i < pix.Length; i++)
            pix[i] = col;
        Texture2D result = new Texture2D(width, height);
        result.SetPixels(pix);
        result.Apply();
        return result;
    }

    // Public methods for manual control
    public void ShowTooltipManual()
    {
        isHovering = true;
        showTooltip = true;
        UpdateTooltipContent();
    }

    public void HideTooltipManual()
    {
        isHovering = false;
        showTooltip = false;
    }

    // Enable/disable tooltip
    public void SetTooltipEnabled(bool enabled)
    {
        this.enabled = enabled;
        if (!enabled)
        {
            showTooltip = false;
            isHovering = false;
        }
    }
}
