using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 🎮 Spectator UI for 5v5 TDM Matches
/// Displays match information, controls, and statistics during spectating
/// </summary>
public class SpectatorUI : MonoBehaviour
{
    [Header("📊 UI References")]
    public Canvas spectatorCanvas;
    public TextMeshProUGUI matchInfoText;
    public TextMeshProUGUI controlsText;
    public TextMeshProUGUI leaderboardText;
    public Button toggleUIButton;

    [Header("🎨 UI Settings")]
    public bool showUI = true;
    public float updateInterval = 1f;
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;

    private SquadManager squadManager;
    private SpectatorCamera spectatorCamera;
    private TournamentManager tournamentManager;
    private float lastUpdateTime;

    void Start()
    {
        // Find required components
        squadManager = FindObjectOfType<SquadManager>();
        spectatorCamera = FindObjectOfType<SpectatorCamera>();
        tournamentManager = FindObjectOfType<TournamentManager>();

        // Create UI if not assigned
        if (spectatorCanvas == null)
        {
            CreateSpectatorUI();
        }

        // Setup update interval
        lastUpdateTime = Time.time;

        Debug.Log("🎮 Spectator UI initialized");
    }

    void Update()
    {
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateUI();
            lastUpdateTime = Time.time;
        }

        // Toggle UI with F12
        if (Input.GetKeyDown(KeyCode.F12))
        {
            ToggleUI();
        }
    }

    void CreateSpectatorUI()
    {
        // Create main canvas
        GameObject canvasObj = new GameObject("SpectatorUI");
        spectatorCanvas = canvasObj.AddComponent<Canvas>();
        spectatorCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        spectatorCanvas.sortingOrder = 100;

        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        canvasObj.AddComponent<GraphicRaycaster>();

        // Create match info panel (top-left)
        CreateMatchInfoPanel();

        // Create controls panel (bottom-left)
        CreateControlsPanel();

        // Create leaderboard panel (top-right)
        CreateLeaderboardPanel();

        // Create toggle button (top-center)
        CreateToggleButton();

        Debug.Log("🎨 Spectator UI created");
    }

    void CreateMatchInfoPanel()
    {
        GameObject panel = CreateUIPanel("MatchInfoPanel", new Vector2(300, 150), new Vector2(10, -10), new Vector2(0, 1));
        matchInfoText = CreateUIText(panel, "MatchInfo", "Match Information");
        matchInfoText.fontSize = 14f;
        matchInfoText.alignment = TextAlignmentOptions.TopLeft;
    }

    void CreateControlsPanel()
    {
        GameObject panel = CreateUIPanel("ControlsPanel", new Vector2(350, 200), new Vector2(10, 10), new Vector2(0, 0));
        controlsText = CreateUIText(panel, "Controls", GetControlsText());
        controlsText.fontSize = 12f;
        controlsText.alignment = TextAlignmentOptions.TopLeft;
    }

    void CreateLeaderboardPanel()
    {
        GameObject panel = CreateUIPanel("LeaderboardPanel", new Vector2(300, 400), new Vector2(-10, -10), new Vector2(1, 1));
        leaderboardText = CreateUIText(panel, "Leaderboard", "Tournament Standings");
        leaderboardText.fontSize = 12f;
        leaderboardText.alignment = TextAlignmentOptions.TopLeft;
    }

    void CreateToggleButton()
    {
        GameObject buttonObj = new GameObject("ToggleUIButton");
        buttonObj.transform.SetParent(spectatorCanvas.transform);

        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(120, 30);
        buttonRect.anchorMin = new Vector2(0.5f, 1f);
        buttonRect.anchorMax = new Vector2(0.5f, 1f);
        buttonRect.anchoredPosition = new Vector2(0, -20);

        toggleUIButton = buttonObj.AddComponent<Button>();
        Image buttonImage = buttonObj.AddComponent<Image>();
        buttonImage.color = new Color(0, 0, 0, 0.7f);

        GameObject buttonTextObj = new GameObject("Text");
        buttonTextObj.transform.SetParent(buttonObj.transform);

        TextMeshProUGUI buttonText = buttonTextObj.AddComponent<TextMeshProUGUI>();
        buttonText.text = "Toggle UI (F12)";
        buttonText.fontSize = 12f;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;

        RectTransform textRect = buttonTextObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        toggleUIButton.onClick.AddListener(ToggleUI);
    }

    GameObject CreateUIPanel(string name, Vector2 size, Vector2 position, Vector2 anchor)
    {
        GameObject panel = new GameObject(name);
        panel.transform.SetParent(spectatorCanvas.transform);

        RectTransform rect = panel.AddComponent<RectTransform>();
        rect.sizeDelta = size;
        rect.anchorMin = anchor;
        rect.anchorMax = anchor;
        rect.anchoredPosition = position;

        Image image = panel.AddComponent<Image>();
        image.color = new Color(0, 0, 0, 0.7f);

        return panel;
    }

    TextMeshProUGUI CreateUIText(GameObject parent, string name, string text)
    {
        GameObject textObj = new GameObject(name);
        textObj.transform.SetParent(parent.transform);

        RectTransform rect = textObj.AddComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.offsetMin = new Vector2(10, 10);
        rect.offsetMax = new Vector2(-10, -10);

        TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
        textComponent.text = text;
        textComponent.color = Color.white;
        textComponent.fontSize = 14f;

        return textComponent;
    }

    void UpdateUI()
    {
        if (!showUI) return;

        UpdateMatchInfo();
        UpdateLeaderboard();
    }

    void UpdateMatchInfo()
    {
        if (matchInfoText == null || squadManager == null) return;

        var stats = squadManager.GetMatchStats();
        string cameraMode = spectatorCamera != null ? spectatorCamera.GetCurrentModeText() : "No Camera";

        string matchInfo = $"<b>🏆 5v5 TDM Match</b>\n\n";
        matchInfo += $"<color=#{ColorUtility.ToHtmlStringRGB(teamAColor)}>🔵 Team A: {stats.teamAScore}</color>\n";
        matchInfo += $"<color=#{ColorUtility.ToHtmlStringRGB(teamBColor)}>🔴 Team B: {stats.teamBScore}</color>\n\n";
        matchInfo += $"⏱️ <b>Time:</b> {stats.matchTime:F0}s\n";
        matchInfo += $"👥 <b>Agents:</b> {stats.teamAAgents}v{stats.teamBAgents}\n";
        matchInfo += $"🎮 <b>Status:</b> {(stats.isActive ? "Active" : "Ended")}\n\n";
        matchInfo += $"📹 <b>{cameraMode}</b>";

        matchInfoText.text = matchInfo;
    }

    void UpdateLeaderboard()
    {
        if (leaderboardText == null || tournamentManager == null) return;

        var topPlayers = tournamentManager.GetTopPlayers(8);
        
        string leaderboard = "<b>🏆 Tournament Standings</b>\n\n";
        
        for (int i = 0; i < topPlayers.Count; i++)
        {
            var player = topPlayers[i];
            string rankIcon = GetRankIcon(i + 1);
            
            leaderboard += $"{rankIcon} <b>{player.name}</b>\n";
            leaderboard += $"   ELO: {player.elo:F0} | {player.currentRank}\n";
            leaderboard += $"   W:{player.wins} L:{player.losses} | KDR:{player.KDR:F2}\n\n";
        }

        leaderboardText.text = leaderboard;
    }

    string GetRankIcon(int rank)
    {
        switch (rank)
        {
            case 1: return "🥇";
            case 2: return "🥈";
            case 3: return "🥉";
            default: return $"{rank}.";
        }
    }

    string GetControlsText()
    {
        return "<b>🎮 Spectator Controls</b>\n\n" +
               "<b>Camera Modes:</b>\n" +
               "F1 - Free Camera\n" +
               "F2 - Overview\n" +
               "F3 - Follow Agent\n" +
               "F4 - Combat Focus\n" +
               "F5 - Cinematic\n\n" +
               "<b>Navigation:</b>\n" +
               "Tab - Next Agent\n" +
               "Shift - Previous Agent\n" +
               "Mouse Wheel - Zoom\n\n" +
               "<b>Free Camera:</b>\n" +
               "WASD + QE - Move\n" +
               "Right Click + Mouse - Look\n\n" +
               "<b>UI:</b>\n" +
               "F12 - Toggle UI";
    }

    public void ToggleUI()
    {
        showUI = !showUI;
        
        if (matchInfoText != null) matchInfoText.gameObject.SetActive(showUI);
        if (controlsText != null) controlsText.gameObject.SetActive(showUI);
        if (leaderboardText != null) leaderboardText.gameObject.SetActive(showUI);

        Debug.Log($"🎮 Spectator UI: {(showUI ? "Shown" : "Hidden")}");
    }

    // Public methods for external control
    public void ShowMatchEndScreen(string winner, int teamAScore, int teamBScore)
    {
        if (matchInfoText != null)
        {
            string endScreen = $"<b>🏁 MATCH ENDED</b>\n\n";
            endScreen += $"🎉 <b>Winner: {winner}</b>\n\n";
            endScreen += $"<color=#{ColorUtility.ToHtmlStringRGB(teamAColor)}>Team A: {teamAScore}</color>\n";
            endScreen += $"<color=#{ColorUtility.ToHtmlStringRGB(teamBColor)}>Team B: {teamBScore}</color>\n\n";
            endScreen += "Next match starting soon...";
            
            matchInfoText.text = endScreen;
        }
    }

    void OnDestroy()
    {
        if (spectatorCanvas != null)
        {
            Destroy(spectatorCanvas.gameObject);
        }
    }
}
