using UnityEngine;
using TMPro;
using UnityEngine.UI;

/// <summary>
/// 💡 Hover Tooltip Showing Role + Gear
/// Displays detailed agent information when hovering over agents in the scene
/// </summary>
public class AgentTooltip : MonoBehaviour
{
    [Header("🖱️ Tooltip Settings")]
    public GameObject tooltipPrefab;
    public float tooltipOffset = 2f;
    public float tooltipDelay = 0.5f;
    public bool followMouse = true;
    public bool showDetailedStats = true;

    [Header("🎨 Visual Settings")]
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;
    public Color neutralColor = Color.white;

    private GameObject tooltipInstance;
    private Camera mainCamera;
    private bool isHovering = false;
    private float hoverStartTime;

    // Agent components
    private VictorAgent victorAgent;
    private SquadMateAgent squadMateAgent;
    private AgentStats agentStats;
    private HealthSystem healthSystem;
    private InventorySystem inventorySystem;

    void Start()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            mainCamera = FindObjectOfType<Camera>();
        }

        // Get agent components
        victorAgent = GetComponent<VictorAgent>();
        squadMateAgent = GetComponent<SquadMateAgent>();
        agentStats = GetComponent<AgentStats>();
        healthSystem = GetComponent<HealthSystem>();
        inventorySystem = GetComponent<InventorySystem>();

        // Create tooltip prefab if not assigned
        if (tooltipPrefab == null)
        {
            CreateDefaultTooltipPrefab();
        }

        // Add collider if missing (needed for mouse detection)
        if (GetComponent<Collider>() == null)
        {
            CapsuleCollider capsule = gameObject.AddComponent<CapsuleCollider>();
            capsule.height = 2f;
            capsule.radius = 0.5f;
            capsule.isTrigger = true;
        }
    }

    void CreateDefaultTooltipPrefab()
    {
        // Create a simple tooltip prefab programmatically
        GameObject tooltip = new GameObject("AgentTooltip");
        
        // Add Canvas
        Canvas canvas = tooltip.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = mainCamera;
        
        // Add Canvas Scaler
        CanvasScaler scaler = tooltip.AddComponent<CanvasScaler>();
        scaler.dynamicPixelsPerUnit = 10f;
        
        // Add background panel
        GameObject panel = new GameObject("Panel");
        panel.transform.SetParent(tooltip.transform);
        
        Image panelImage = panel.AddComponent<Image>();
        panelImage.color = new Color(0f, 0f, 0f, 0.8f);
        
        RectTransform panelRect = panel.GetComponent<RectTransform>();
        panelRect.sizeDelta = new Vector2(300f, 200f);
        panelRect.anchoredPosition = Vector2.zero;
        
        // Add text component
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(panel.transform);
        
        TextMeshProUGUI text = textObj.AddComponent<TextMeshProUGUI>();
        text.text = "Agent Info";
        text.fontSize = 14f;
        text.color = Color.white;
        text.alignment = TextAlignmentOptions.TopLeft;
        
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = new Vector2(10f, 10f);
        textRect.offsetMax = new Vector2(-10f, -10f);
        
        tooltipPrefab = tooltip;
        
        // Make it a prefab for reuse
        tooltip.SetActive(false);
    }

    void OnMouseEnter()
    {
        if (!isHovering)
        {
            isHovering = true;
            hoverStartTime = Time.time;
            
            if (tooltipDelay <= 0f)
            {
                ShowTooltip();
            }
            else
            {
                Invoke(nameof(ShowTooltip), tooltipDelay);
            }
        }
    }

    void OnMouseExit()
    {
        isHovering = false;
        CancelInvoke(nameof(ShowTooltip));
        HideTooltip();
    }

    void Update()
    {
        if (tooltipInstance != null && followMouse && mainCamera != null)
        {
            // Position tooltip near mouse cursor
            Vector3 mousePos = Input.mousePosition;
            Vector3 worldPos = mainCamera.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, mainCamera.nearClipPlane + 5f));
            tooltipInstance.transform.position = worldPos;
        }
    }

    void ShowTooltip()
    {
        if (!isHovering || tooltipInstance != null) return;

        Vector3 tooltipPosition = transform.position + Vector3.up * tooltipOffset;
        
        if (followMouse && mainCamera != null)
        {
            Vector3 mousePos = Input.mousePosition;
            tooltipPosition = mainCamera.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, mainCamera.nearClipPlane + 5f));
        }

        tooltipInstance = Instantiate(tooltipPrefab, tooltipPosition, Quaternion.LookRotation(mainCamera.transform.forward));
        tooltipInstance.SetActive(true);

        // Update tooltip content
        UpdateTooltipContent();
    }

    void HideTooltip()
    {
        if (tooltipInstance != null)
        {
            Destroy(tooltipInstance);
            tooltipInstance = null;
        }
    }

    void UpdateTooltipContent()
    {
        if (tooltipInstance == null) return;

        TextMeshProUGUI tooltipText = tooltipInstance.GetComponentInChildren<TextMeshProUGUI>();
        if (tooltipText == null) return;

        string content = BuildTooltipContent();
        tooltipText.text = content;

        // Update background color based on team
        Image backgroundImage = tooltipInstance.GetComponentInChildren<Image>();
        if (backgroundImage != null)
        {
            Color teamColor = GetTeamColor();
            backgroundImage.color = new Color(teamColor.r, teamColor.g, teamColor.b, 0.8f);
        }
    }

    string BuildTooltipContent()
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();

        // Agent name and basic info
        sb.AppendLine($"<b><size=16>{gameObject.name}</size></b>");
        sb.AppendLine();

        // Role information
        if (victorAgent != null)
        {
            sb.AppendLine($"🧠 <b>Role:</b> {victorAgent.assignedRole}");
            sb.AppendLine($"👥 <b>Team:</b> {victorAgent.teamID}");
        }
        else if (squadMateAgent != null)
        {
            sb.AppendLine($"🧠 <b>Role:</b> SquadMate");
            sb.AppendLine($"👥 <b>Team:</b> {(gameObject.tag.Contains("TeamA") ? "Team A" : "Team B")}");
        }

        // Health information
        if (healthSystem != null)
        {
            float healthPercent = (healthSystem.currentHealth / healthSystem.maxHealth) * 100f;
            sb.AppendLine($"❤️ <b>Health:</b> {healthSystem.currentHealth:F0}/{healthSystem.maxHealth:F0} ({healthPercent:F0}%)");
        }

        // Weapon information
        if (inventorySystem != null)
        {
            string weapon = string.IsNullOrEmpty(inventorySystem.equippedWeapon) ? "None" : inventorySystem.equippedWeapon;
            sb.AppendLine($"🔫 <b>Weapon:</b> {weapon}");
            
            if (inventorySystem.medkitCount > 0)
            {
                sb.AppendLine($"💊 <b>Medkits:</b> {inventorySystem.medkitCount}");
            }
        }

        // Detailed stats (if enabled)
        if (showDetailedStats && agentStats != null)
        {
            sb.AppendLine();
            sb.AppendLine("<b>📊 Combat Stats:</b>");
            sb.AppendLine($"• Kills: {agentStats.kills}");
            sb.AppendLine($"• Deaths: {agentStats.deaths}");
            sb.AppendLine($"• K/D Ratio: {agentStats.GetKDR():F2}");
            sb.AppendLine($"• Revives: {agentStats.revives}");
            sb.AppendLine($"• Zones Held: {agentStats.zonesHeld}");
        }

        // Current status
        sb.AppendLine();
        sb.AppendLine($"🎯 <b>Status:</b> {GetAgentStatus()}");

        return sb.ToString();
    }

    Color GetTeamColor()
    {
        if (victorAgent != null)
        {
            return victorAgent.teamID == TeamID.TeamA ? teamAColor : teamBColor;
        }
        else if (squadMateAgent != null)
        {
            return gameObject.tag.Contains("TeamA") ? teamAColor : teamBColor;
        }
        return neutralColor;
    }

    string GetAgentStatus()
    {
        if (healthSystem != null && healthSystem.currentHealth <= 0)
        {
            return "💀 Eliminated";
        }

        if (victorAgent != null)
        {
            // Add more specific status based on agent behavior
            if (victorAgent.currentTarget != null)
            {
                return "⚔️ In Combat";
            }
            else if (inventorySystem != null && inventorySystem.medkitCount > 0 && healthSystem != null && healthSystem.currentHealth < healthSystem.maxHealth * 0.5f)
            {
                return "🩹 Healing";
            }
            else
            {
                return "🔍 Patrolling";
            }
        }

        return "🤖 Active";
    }

    void OnDestroy()
    {
        HideTooltip();
    }

    // Public method to manually show tooltip (for UI buttons, etc.)
    public void ShowTooltipManual()
    {
        isHovering = true;
        ShowTooltip();
    }

    // Public method to manually hide tooltip
    public void HideTooltipManual()
    {
        isHovering = false;
        HideTooltip();
    }
}
